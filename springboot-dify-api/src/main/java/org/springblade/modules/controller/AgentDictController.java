package org.springblade.modules.controller;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.service.AgentDictService;
import org.springblade.modules.service.ChatHistoryService;
import org.springblade.modules.service.UserAgentListService;
import org.springblade.modules.service.UserService;
import org.springblade.modules.vo.MobileAgentParamVO;
import org.springblade.modules.vo.UserVO;
import org.springblade.modules.vo.WpsAgentParamVO;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/enclosure/agentDict")
@RequiredArgsConstructor
public class AgentDictController {

    private final AgentDictService agentDictService;

    private final UserAgentListService userAgentListService;

    private final ChatHistoryService chatHistoryService;

    private final MobileAgentParamVO mobileAgentParamVO;

    private final WpsAgentParamVO wpsAgentParamVO;

    private final UserService userService;

    @GetMapping("/findAll")
    public List<AgentDict> findAll() {
        return agentDictService.findAll();
    }

    @GetMapping("/getAgentDictByIsDefaultTrue")
    public AgentDict getAgentDictByIsDefaultTrue() {
        return agentDictService.getAgentDictByIsDefaultTrue();
    }

    @GetMapping("/findByIsDefaultFalse")
    public List<AgentDict> findByIsDefaultFalse() {
        return agentDictService.findByIsDefaultFalse();
    }

    /**
     * 获取当前用户可见的所有AgentDict
     */
    @GetMapping("/findAllVisible")
    public List<AgentDict> findAllVisible() {
        return agentDictService.findAllVisibleToCurrentUser();
    }

    @PostMapping("/save")
    public AgentDict save(@RequestBody AgentDict agentDict) {

        return agentDictService.save(agentDict);
    }

    @GetMapping("/delete")
    public void delete(@RequestParam("id") String id) {
        agentDictService.delete(id);
        userAgentListService.removeByAgentDictId(id);
        chatHistoryService.deleteByAgentDictId(id);
    }

    @GetMapping("/getMobileAgentParam")
    public MobileAgentParamVO getMobileAgentParamVO() {
        return mobileAgentParamVO;
    }

    @GetMapping("/getWpsAgentParam")
    public WpsAgentParamVO getWpsAgentParamVO() {
        return wpsAgentParamVO;
    }

    /**
     * 更新AgentDict的可见性设置
     * @param id AgentDict ID
     * @param isPublic 是否公开可见
     * @param visibleUserIds 可见用户ID列表
     * @return 更新结果
     */
    @PostMapping("/updateVisibility")
    public JSONObject updateVisibility(@RequestParam("id") String id,
                                     @RequestParam("isPublic") Boolean isPublic,
                                     @RequestBody(required = false) List<String> visibleUserIds) {
        AgentDict agentDict = agentDictService.updateVisibility(id, isPublic, visibleUserIds);
        JSONObject result = JSONUtil.createObj();
        if (agentDict != null) {
            result.set("code", 200);
            result.set("msg", "更新成功");
            result.set("data", agentDict);
        } else {
            result.set("code", 400);
            result.set("msg", "更新失败，未找到指定的AgentDict");
        }
        return result;
    }

    /**
     * 获取AgentDict的可见性设置
     * @param id AgentDict ID
     * @return 可见性设置信息
     */
    @GetMapping("/getVisibility")
    public JSONObject getVisibility(@RequestParam("id") String id) {
        AgentDict agentDict = agentDictService.findById(id);
        JSONObject result = JSONUtil.createObj();
        if (agentDict != null) {
            result.set("code", 200);
            result.set("isPublic", agentDict.getIsPublic());
            result.set("visibleToUsers", agentDict.getVisibleToUsers());
        } else {
            result.set("code", 400);
            result.set("msg", "未找到指定的AgentDict");
        }
        return result;
    }

    /**
     * 获取AgentDict的可见用户列表
     * @param id AgentDict ID
     * @return 可见用户列表
     */
    @GetMapping("/getVisibleUsers")
    public JSONObject getVisibleUsers(@RequestParam("id") String id) {
        AgentDict agentDict = agentDictService.findById(id);
        JSONObject result = JSONUtil.createObj();

        if (agentDict == null) {
            result.set("code", 400);
            result.set("msg", "未找到指定的AgentDict");
            return result;
        }

        List<UserVO> visibleUsers = new ArrayList<>();

        // 如果是公开的AgentDict，返回空列表
        if (agentDict.getIsPublic() != null && agentDict.getIsPublic()) {
            result.set("code", 200);
            result.set("data", visibleUsers);
            return result;
        }

        // 如果有可见用户列表，解析并获取用户信息
        if (StrUtil.isNotBlank(agentDict.getVisibleToUsers())) {
            try {
                JSONArray userIds = JSONUtil.parseArray(agentDict.getVisibleToUsers());
                for (int i = 0; i < userIds.size(); i++) {
                    String userId = userIds.getStr(i);
                    UserVO user = userService.findUserById(userId);
                    if (user != null) {
                        visibleUsers.add(user);
                    }
                }
            } catch (Exception e) {
                // 如果解析失败，返回空列表
                result.set("code", 200);
                result.set("data", new ArrayList<>());
                return result;
            }
        }

        result.set("code", 200);
        result.set("data", visibleUsers);
        return result;
    }
}