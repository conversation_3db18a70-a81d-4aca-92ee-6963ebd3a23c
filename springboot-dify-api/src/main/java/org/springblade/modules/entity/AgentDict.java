package org.springblade.modules.entity;

import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

import java.util.UUID;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 23:51
 */
@Data
@Builder
@Entity
@Table(name = "portal_agent_dict")
@NoArgsConstructor
@AllArgsConstructor
public class AgentDict {

    @Id
    @GeneratedValue(generator = "uuid2")
    @GenericGenerator(name = "uuid2", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(36)")
    private String id;

    @Column(name = "api_server_host", length = 255)
    private String apiServerHost;

    @Column(name = "name", length = 255)
    private String name;

    @Column(name = "api_key", length = 255)
    private String apiKey;

    @Column(name = "is_default", columnDefinition = "TINYINT(1)")
    private Boolean isDefault;

    @Column(name = "sort_order")
    private Integer sortOrder;

    @Column(name = "icon", length = 1000)
    private String icon;

    @Column(name = "icon_color")
    private String iconColor;

    /**
     * 逻辑删除标记（true表示已删除）
     */
    @Column(name = "is_deleted", columnDefinition = "TINYINT(1)")
    private Boolean isDeleted;

    @Column(name = "description")
    private String description;

    @Column(name = "type")
    private String type;

    @Column(name = "super_sonic_agent_id")
    private String superSonicAgentId;

    /**
     * 是否公开可见（true表示所有用户可见，false表示仅对特定用户可见）
     */
    @Column(name = "is_public", columnDefinition = "TINYINT(1) DEFAULT 1")
    private Boolean isPublic = true;

    /**
     * 可见用户ID列表，使用JSON数组存储，如：["user1", "user2"]
     */
    @Column(name = "visible_to_users", columnDefinition = "TEXT")
    private String visibleToUsers;
}
