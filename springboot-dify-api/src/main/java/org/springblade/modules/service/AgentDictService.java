package org.springblade.modules.service;

import cn.hutool.json.JSONUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springblade.modules.entity.AgentDict;
import org.springblade.modules.repository.AgentDictRepository;
import org.springblade.qyweixin.security.CustomUserDetails;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> [sijun.zeng]
 * @date 2025-03-17 23:54
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgentDictService {

    private final AgentDictRepository agentDictRepository;
    private final HttpServletRequest request;

    public AgentDict findById(String id) {
        return agentDictRepository.findById(id).orElse(null);
    }

    public AgentDict findByApiKey(String apiKey) {
        return agentDictRepository.findByApiKey(apiKey);
    }

    public AgentDict findByApiKeyAndApiServerHost(String apiKey, String apiServerHost) {
        return agentDictRepository.findFirstByApiKeyAndApiServerHost(apiKey, apiServerHost);
    }

    public List<AgentDict> findAll() {
        return agentDictRepository.findAll();
    }

    public AgentDict getAgentDictByIsDefaultTrue() {
        return agentDictRepository.getAgentDictByIsDefaultTrue();
    }

    public List<AgentDict> findByIsDefaultFalse() {
        return agentDictRepository.findByIsDefaultFalseAndIsDeletedFalseOrderBySortOrderAsc();
    }

    public AgentDict save(AgentDict agentDict) {
        if (agentDict.getId() == null) {
            Integer maxOrder = agentDictRepository.findOrderMax();
            agentDict.setIsDefault(false);
            agentDict.setSortOrder(maxOrder == null ? 1 : maxOrder + 1);
        }
        agentDict.setIsDeleted(false);
        AgentDict save = agentDictRepository.save(agentDict);
        return save;
    }

    public void delete(String id) {
        AgentDict agentDict = agentDictRepository.findById(id).orElse(null);
        if (agentDict != null) {
            agentDict.setIsDeleted(true);
            agentDictRepository.save(agentDict);
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                return principal.getAttributes().getId();
            }
        } catch (Exception e) {
            log.error("获取当前用户ID失败", e);
        }
        return null;
    }

    /**
     * 判断当前用户是否为管理员
     * @return 是否为管理员
     */
    private boolean isCurrentUserAdmin() {
        try {
            UsernamePasswordAuthenticationToken userPrincipal = (UsernamePasswordAuthenticationToken) request.getUserPrincipal();
            if (userPrincipal != null) {
                CustomUserDetails principal = (CustomUserDetails) userPrincipal.getPrincipal();
                // 判断用户名是否为"gzyc"
                return "1044010100000218".equals(principal.getAttributes().getName());
            }
        } catch (Exception e) {
            log.error("判断用户是否为管理员失败", e);
        }
        return false;
    }

    /**
     * 查询当前用户可见的所有AgentDict
     * 如果是管理员，则返回所有非默认且未删除的AgentDict
     */
    public List<AgentDict> findAllVisibleToCurrentUser() {
        // 判断是否为管理员
        if (isCurrentUserAdmin()) {
            // 管理员可以看到所有非默认且未删除的AgentDict
            return agentDictRepository.findByIsDefaultFalseAndIsDeletedFalseOrderBySortOrderAsc();
        }

        String userId = getCurrentUserId();
        if (userId == null) {
            // 如果无法获取用户ID，只返回公开的AgentDict
            return agentDictRepository.findPublicAgentDicts();
        }

        // 使用引号包裹的用户ID进行查询，以匹配JSON数组中的格式
        String userIdQuoted = '"' + userId + '"';
        return agentDictRepository.findAllAgentDictsVisibleToUser(userId, userIdQuoted);
    }

    /**
     * 设置AgentDict的可见性
     * @param id AgentDict ID
     * @param isPublic 是否公开可见
     * @param visibleUserIds 可见用户ID列表
     * @return 更新后的AgentDict
     */
    public AgentDict updateVisibility(String id, Boolean isPublic, List<String> visibleUserIds) {
        AgentDict agentDict = agentDictRepository.findById(id).orElse(null);
        if (agentDict != null) {
            agentDict.setIsPublic(isPublic);
            if (visibleUserIds != null) {
                agentDict.setVisibleToUsers(JSONUtil.toJsonStr(visibleUserIds));
            } else if (!isPublic) {
                // 如果设置为非公开但没有提供可见用户列表，初始化为空数组
                agentDict.setVisibleToUsers(JSONUtil.toJsonStr(new ArrayList<String>()));
            }
            return agentDictRepository.save(agentDict);
        }
        return null;
    }
}
