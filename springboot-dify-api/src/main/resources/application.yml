spring:
  application:
    name: dify-demo
  session:
    timeout: 2147483647
    driver-class-name: com.mysql.cj.jdbc.Driver
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQLDialect
        format_sql: true
  profiles:
    default: dev
  datasource:
    url: ${DB_DATASOURCE_JDBC_URL:****************************************************************************************************************}
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 30  # Reduced from 200
      minimum-idle: 10       # Reduced from 50
      idle-timeout: 300000
      max-lifetime: 1200000  # Reduced from 1800000
      connection-timeout: 30000
      leak-detection-threshold: 60000  # Add leak detection
  data:
    # Redis配置
    redis:
      host: ${REDIS_HOST:127.0.0.1}
      port: ${REDIS_PORT:6379}
      password: ${REDIS_PASSWORD:}
      database: ${REDIS_DATABASE:15}
      timeout: 10000
      lettuce:
        pool:
          max-active: 8
          max-wait: -1
          max-idle: 8
          min-idle: 0
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true


wechat:
  corp-id: ${WECHAT_CORP_ID:wwe89a7c2d3b3c180f}
  agent-id: ${WECHAT_AGENT_ID:1000058}
  secret: ${WECHAT_SECRET:gUiU2kD_ojs9ZKIWRf0HaaJCFSc4Rh7rIqMGqM50e78}
  second-level-domain-names: ${SECOND_LEVEL_DOMAIN_NAMES:ai-portal}

base:
  url: http://localhost:8080/
  home-url: http://localhost:8080/home
#  url: http://**************:32696/
#  home-url: http://**************:32696/home

qywx:
  redirect-uri: ${QYWX_REDIRECT_URI:https://www.gz-tobacco.net:9999/qy_wechat_h5/ai-portal/login/}
  url: ${QYWX_URL:https://www.gz-tobacco.net:9999/qy_wechat_h5/ai-portal/index.html#/qywxLogin}
  login-url: /login/

# IDaaS配置
idaas:
#  web-url: /index.html#/idaasLogin
  web-url: ${IDAAS_WEB_URL:https://ai.gz-tobacco.net/index.html#/idaasLogin}
  server-host: http://************
  # 客户端ID，根据实际IDaaS提供商配置
  client-id: adb35a375769ae12e21c7b618f11c430I2yxV2x9JVs
  # 客户端密钥，根据实际IDaaS提供商配置
  client-secret: BQv0fvWmYvQs7p1zr9ZrWyfnVu8ufTptlxuiFGJWKg
  # 重定向URI，用于接收授权码
#  redirect-uri: http://**************:32682/idaas/login/
  redirect-uri: ${IDAAS_REDIRECT_URL:https://ai.gz-tobacco.net/idaas/login/}
  # 登录URL路径
  login-url: /idaas/login/

server:
  servlet:
    session:
      timeout: 2147483647
  tomcat:
    max-http-form-post-size: 100MB
    max-swallow-size: 100MB
    max-http-response-header-size: 100MB
  max-http-request-header-size: 100MB

# JWT Configuration
jwt:
  secret: YueShuTechSecretKeyForJWTAuthenticationInDifyAPI2024
  # 设置为-1表示Token永不过期
  expiration: -1

gzyc:
  userInfoUrl: http://*************:8000/DATASHARING/service/user_info
  username: 'liangsijie'
  ak: 'AK-R7eSxSLvb4Q3CkTb'
  sk: 'SK-FFAyP57jP5bwSXXhP4samYcxXjRBwDkf'

mobile:
  is_mobile: ${IS_MOBILE:false}
  api_server_host: ${MOBILE_API_SERVER_HOST:http://127.0.0.1/v1}
  api_key: ${MOBILE_API_KEY:app-darPZCKtoOwzgfyAnjCtclhT}
  name: ${MOBILE_NAME:超级智能体-移动端}

wps:
  api_server_host: ${WPS_API_SERVER_HOST:http://127.0.0.1/v1}
  api_key: ${WPS_API_KEY:app-darPZCKtoOwzgfyAnjCtclhT}
  name: ${WPS_NAME:超级智能体-WPS}

chat-voice:
  url: ${CHAT_VOICE_URL:http://localhost:8000}
  container: ${CHAT_CONTAINER_URL:http://localhost:8000}

#  minio配置
minio:
  endpoint: ${MINIO_ENDPOINT:http://127.0.0.1:9000}
  access-key: ${MINIO_ACCESS_KEY:yueshu2025}
  secret-key: ${MINIO_SECRET_KEY:yueshu2025}
  bucket-name: ${MINIO_BUCKET_NAME:enclosure-icons}
  transform-endpoint: ${MINIO_TRANSFORM_ENDPOINT:http://localhost:9000}

dify:
  dataset:
    api-key: ${DIFY_DATASET_API_KEY:dataset-mrWJe1yMgdwYWm3J0VNqJGpU}
    api-server-host: ${DIFY_DATASET_API_SERVER_HOST:http://127.0.0.1/v1}
    search-ids: ${DIFY_DATASET_SEARCH_IDS:a4fe2481-8685-459a-9fdf-3eee72a1e03b,06490308-8089-4d53-95a8-3f88447e9739}
